package br.com.alice.ehr.consumers

import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory.buildConsolidatedRewards
import br.com.alice.data.layer.helpers.TestModelFactory.buildCounterReferral
import br.com.alice.data.layer.helpers.TestModelFactory.buildHealthProfessional
import br.com.alice.data.layer.models.ConsolidatedRewardsClinicalRecords
import br.com.alice.data.layer.models.ConsolidatedRewardsType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.NotOccurredReason
import br.com.alice.ehr.converters.toModel
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.Test
import kotlinx.coroutines.runBlocking

class CounterReferralConsumerTest : ConsumerTest() {
    private val consolidatedRewardsService: ConsolidatedRewardsService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()

    val consumer = CounterReferralConsumer(
        consolidatedRewardsService = consolidatedRewardsService, healthProfessionalService = healthProfessionalService
    )
    private val counterReferral = buildCounterReferral()
    private val event = CounterReferralCreatedEvent(counterReferral)
    private val healthProfessional = buildHealthProfessional()
    private val consolidatedRewards = buildConsolidatedRewards()
    private val createdAtZone = counterReferral.createdAt.toSaoPauloTimeZone().toLocalDate()

    @Test
    fun `processCounterReferral should return success when appointment is counter referral`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = false
        ) {
            coEvery {
                healthProfessionalService.findByStaffId(
                    counterReferral.staffId, FindOptions(
                        withStaff = false,
                        withContact = false
                    )
                )
            } returns healthProfessional.success()

            coEvery {
                consolidatedRewardsService.getOrCreate(
                    healthProfessional.id,
                    createdAtZone
                )
            } returns consolidatedRewards.toModel().success()
            val toUpdate = consolidatedRewards.copy(
                clinicalRecords = consolidatedRewards.clinicalRecords + ConsolidatedRewardsClinicalRecords(
                    id = counterReferral.id,
                    type = ConsolidatedRewardsType.COUNTER_REFERRAL,
                    createdAt = createdAtZone,
                    appointmentDate = counterReferral.appointmentDate
                )
            ).toModel()
            coEvery {
                consolidatedRewardsService.update(toUpdate)
            } returns toUpdate.success()

            val result = consumer.processCounterReferral(event)
            assertThat(result).isSuccessWithData(toUpdate)

            coVerifyOnce { healthProfessionalService.findByStaffId(any(), any()) }
            coVerifyOnce { consolidatedRewardsService.getOrCreate(any(), any()) }
        }
    }

    @Test
    fun `processCounterReferral should return success when is appointment`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = false
        ) {
            val counterReferral = counterReferral.copy(appointmentId = counterReferral.id)
            val event = CounterReferralCreatedEvent(counterReferral)

            coEvery {
                healthProfessionalService.findByStaffId(counterReferral.staffId)
            } returns healthProfessional.success()

            coEvery {
                consolidatedRewardsService.getOrCreate(
                    healthProfessional.id,
                    createdAtZone
                )
            } returns consolidatedRewards.toModel().success()
            val toUpdate = consolidatedRewards.copy(
                clinicalRecords = consolidatedRewards.clinicalRecords + ConsolidatedRewardsClinicalRecords(
                    id = counterReferral.appointmentId!!,
                    type = ConsolidatedRewardsType.APPOINTMENT,
                    createdAt = createdAtZone,
                    appointmentDate = counterReferral.appointmentDate
                )
            ).toModel()
            coEvery {
                consolidatedRewardsService.update(toUpdate)
            } returns toUpdate.success()

            val result = consumer.processCounterReferral(event)
            assertThat(result).isSuccessWithData(toUpdate)

            coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
            coVerifyOnce { consolidatedRewardsService.getOrCreate(any(), any()) }
        }
    }

    @Test
    fun `processCounterReferral should not process when cr not occurred`() = runBlocking {
        withFeatureFlag(
            FeatureNamespace.EHR,
            key = "send_process_appointment",
            value = false
        ) {
            val counterReferral = counterReferral.copy(notOccurredReason = NotOccurredReason.NO_SHOW)
            val event = CounterReferralCreatedEvent(counterReferral)

            val result = consumer.processCounterReferral(event)
            assertThat(result).isSuccessWithData(false)

            coVerifyNone { healthProfessionalService.findByStaffId(any()) }
            coVerifyNone { consolidatedRewardsService.getOrCreate(any(), any()) }
            coVerifyNone { consolidatedRewardsService.update(any()) }
        }
    }
}
