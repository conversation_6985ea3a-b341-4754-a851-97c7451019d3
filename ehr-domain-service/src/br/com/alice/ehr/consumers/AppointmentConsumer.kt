package br.com.alice.ehr.consumers

import br.com.alice.appointment.client.AppointmentService
import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.appointment.event.AppointmentCreatedEvent
import br.com.alice.appointment.event.DraftAppointmentDeletedEvent
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.AppointmentDiscardedType
import br.com.alice.data.layer.models.AppointmentType.ANNOTATION
import br.com.alice.data.layer.models.AppointmentType.ANNOTATION_HEALTH_COMMUNITY
import br.com.alice.data.layer.models.AppointmentType.COUNTER_REFERRAL
import br.com.alice.data.layer.models.AppointmentType.STATEMENT_OF_HEALTH
import br.com.alice.data.layer.models.ConsolidatedRewardsClinicalRecordsModel
import br.com.alice.data.layer.models.ConsolidatedRewardsType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.ehr.services.internal.CounterReferralInternalService
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class AppointmentConsumer(
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val staffService: StaffService,
    private val counterReferralService: CounterReferralService,
    private val counterReferralInternalService: CounterReferralInternalService,
    private val appointmentService: AppointmentService,
    private val healthProfessionalService: HealthProfessionalService,
    private val consolidatedRewardsService: ConsolidatedRewardsService,
) : Consumer() {

    private val noProcessTypes = listOf(ANNOTATION, ANNOTATION_HEALTH_COMMUNITY, STATEMENT_OF_HEALTH)

    suspend fun addStaffOnPersonClinicalAccount(event: AppointmentCreatedEvent) = withSubscribersEnvironment {
        val appointment = event.payload.appointment

        logger.info(
            "AppointmentConsumer received AppointmentCreatedEvent to add Staff on PersonClinicalAccount",
            "person_id" to appointment.personId,
            "staff_id" to appointment.staffId,
            "type" to appointment.type,
            "appointment_id" to appointment.id,
        )

        if (noProcessTypes.contains(appointment.type)) return@withSubscribersEnvironment true.success()

        staffService.get(appointment.staffId).flatMap { staff ->
            if (staff.isFromMultiTeam()) {
                personClinicalAccountService.addStaffOnMultiTeam(
                    personId = appointment.personId, staff = staff
                )
            } else true.success()
        }.coFoldNotFound {
            true.success()
        }
    }

    suspend fun addCounterReferralByAppointment(event: AppointmentCompletedEvent) = withSubscribersEnvironment {
        val appointment = event.payload.appointment
        if (counterReferralService.getByAppointmentId(appointment.id).getOrNullIfNotFound() != null)
            return@withSubscribersEnvironment true.success()

        counterReferralInternalService.buildCounterReferral(event.payload.appointment).flatMap { cr ->
            counterReferralService.create(cr)
        }.then {
            logger.info(
                "CounterReferral created by AppointmentCompletedEvent",
                "appointment_id" to appointment.id,
                "counter_referral_id" to it.id,
                "person_id" to appointment.personId,
                "health_community_specialist_id" to appointment.staffId,
                "appointment_date" to it.appointmentDate,
            )
        }.coFoldException(InvalidArgumentException::class) { false.success() }
    }


    suspend fun addCounterReferralByAppointmentDeleted(event: DraftAppointmentDeletedEvent) =
        withSubscribersEnvironment {
            val payload = event.payload
            if (payload.type != COUNTER_REFERRAL || payload.discardedType != AppointmentDiscardedType.NO_SHOW) return@withSubscribersEnvironment true.success()
            if (counterReferralService.getByAppointmentId(payload.appointmentId).getOrNullIfNotFound() != null)
                return@withSubscribersEnvironment true.success()
            val appointment = appointmentService.get(payload.appointmentId).get()

            counterReferralInternalService.buildCounterReferralNoShow(
                appointment = appointment,
            ).flatMap {
                counterReferralService.create(it)
            }.coFoldException(InvalidArgumentException::class) { false.success() }
        }

    suspend fun processAppointment(event: AppointmentCompletedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {

            val shouldProcessAppointment = FeatureService.get(
                namespace = FeatureNamespace.EHR,
                key = "send_process_appointment",
                defaultValue = false
            )

            if (!shouldProcessAppointment) return@withSubscribersEnvironment true.success()

            val appointment = event.payload.appointment
            val completedAt = appointment.completedAt
                ?: return@withSubscribersEnvironment InvalidArgumentException("Appointment completedAt cannot be null").failure()
            val appointmentDate = appointment.appointmentDate
                ?: return@withSubscribersEnvironment InvalidArgumentException("Appointment appointmentDate cannot be null").failure()
            val completedAtZone = completedAt.toSaoPauloTimeZone().toLocalDate()

            healthProfessionalService.findByStaffId(appointment.staffId).flatMap {
                consolidatedRewardsService.getOrCreate(it.id, completedAtZone)
            }.flatMap {
                val record = ConsolidatedRewardsClinicalRecordsModel(
                    id = appointment.id,
                    type = ConsolidatedRewardsType.APPOINTMENT,
                    createdAt = completedAtZone,
                    appointmentDate = appointmentDate
                )
                consolidatedRewardsService.update(it.copy(clinicalRecords = it.clinicalRecords + record))
            }
        }

}
