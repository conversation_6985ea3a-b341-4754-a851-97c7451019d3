package br.com.alice.ehr.consumers

import br.com.alice.common.core.extensions.toSaoPauloTimeZone
import br.com.alice.data.layer.models.ConsolidatedRewardsClinicalRecordsModel
import br.com.alice.data.layer.models.ConsolidatedRewardsType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class CounterReferralConsumer(
    private val consolidatedRewardsService: ConsolidatedRewardsService,
    private val healthProfessionalService: HealthProfessionalService,
) : Consumer() {

    suspend fun processCounterReferral(event: CounterReferralCreatedEvent): Result<Any, Throwable> =
        withSubscribersEnvironment {
            val shouldProcessAppointment = FeatureService.get(
                namespace = FeatureNamespace.EHR,
                key = "send_process_appointment",
                defaultValue = false
            )

            if (shouldProcessAppointment) return@withSubscribersEnvironment true.success()

            val cr = event.payload.counterReferral
            if (cr.notOccurredReason != null)
                return@withSubscribersEnvironment false.success()
            val createdAtZone = cr.createdAt.toSaoPauloTimeZone().toLocalDate()
            healthProfessionalService.findByStaffId(cr.staffId).flatMap {
                consolidatedRewardsService.getOrCreate(it.id, createdAtZone)
            }.flatMap {
                val record = ConsolidatedRewardsClinicalRecordsModel(
                    id = cr.appointmentId ?: cr.id,
                    type = if (cr.appointmentId != null) ConsolidatedRewardsType.APPOINTMENT else ConsolidatedRewardsType.COUNTER_REFERRAL,
                    createdAt = createdAtZone,
                    appointmentDate = cr.appointmentDate
                )
                consolidatedRewardsService.update(it.copy(clinicalRecords = it.clinicalRecords + record))
            }
        }
}
